{"name": "news-service", "version": "1.0.0", "description": "News/Content Service for Anti-Fraud Platform", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "build": "echo 'No build step required for Node.js service'", "test:contracts": "jest tests/contracts/ || echo \"No contract tests found\"", "test:integration": "jest tests/integration/ || echo \"No integration tests found\"", "security:check": "npm audit --audit-level=moderate", "circuit-breaker:status": "curl -s http://localhost:8080/circuit-breaker/status || echo \"Circuit breaker not available\"", "test:unit": "jest tests/unit/ || npm test", "security:fix": "npm audit fix", "health:check": "curl -s http://localhost:3005/health || echo \"Service not running\"", "metrics": "curl -s http://localhost:3005/metrics || echo \"Metrics not available\"", "logs:tail": "tail -f logs/*.log || echo \"No logs found\"", "dev:debug": "NODE_ENV=development DEBUG=* npm start", "prod:start": "NODE_ENV=production npm start"}, "dependencies": {"axios": "^1.6.2", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "firebase-admin": "^11.11.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-cron": "^3.0.3", "prom-client": "^15.1.0", "redis": "^4.6.10", "rss-parser": "^3.13.0", "uuid": "^11.1.0", "winston": "^3.11.0"}, "devDependencies": {"@pact-foundation/pact": "^12.1.0", "@pact-foundation/pact-node": "^10.18.0", "@types/jest": "^29.5.8", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["news", "content", "microservice", "aggregation", "rss", "articles"], "author": "Anti-Fraud Platform Team", "license": "MIT", "config": {"techStack": {"circuitBreaker": false, "eventBus": true, "serviceAuth": true, "monitoring": true}}}