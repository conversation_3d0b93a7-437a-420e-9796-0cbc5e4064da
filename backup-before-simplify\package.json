{"name": "factcheck-platform", "version": "1.1.0", "description": "FactCheck Anti-Fraud Platform - Enhanced Microservices Architecture with Advanced Tech Stack", "private": true, "scripts": {"start": "node scripts/unified-deployment.js local", "start:full": "node scripts/unified-deployment.js local --with-client", "start:safe": "node scripts/utils/fix-port-conflicts.js && npm run start", "dev": "node scripts/unified-deployment.js local --dev", "stop": "node scripts/utils/kill-all-services.js", "restart": "npm run stop && npm run start", "clean": "npm run stop && npm run docker:down && docker system prune -f", "setup": "npm run install:all && npm run setup:env", "start:client": "cd client && npm start", "start:services": "concurrently --kill-others-on-fail \"npm run start:auth\" \"npm run start:api-gateway\" \"npm run start:admin\" \"npm run start:chat\" \"npm run start:community\" \"npm run start:link\" \"npm run start:news\"", "start:auth": "cd services/auth-service && npm start", "start:api-gateway": "cd services/api-gateway && npm start", "start:admin": "cd services/admin-service && npm start", "start:chat": "cd services/chat-service && npm start", "start:community": "cd services/community-service && npm start", "start:link": "cd services/link-service && npm start", "start:news": "cd services/news-service && npm start", "dev:services": "concurrently --kill-others-on-fail \"npm run dev:auth\" \"npm run dev:api-gateway\" \"npm run dev:admin\" \"npm run dev:chat\" \"npm run dev:community\" \"npm run dev:link\" \"npm run dev:news\"", "dev:auth": "cd services/auth-service && npm run dev", "dev:api-gateway": "cd services/api-gateway && npm run dev", "dev:admin": "cd services/admin-service && npm run dev", "dev:chat": "cd services/chat-service && npm run dev", "dev:community": "cd services/community-service && npm run dev", "dev:link": "cd services/link-service && npm run dev", "dev:news": "cd services/news-service && npm run dev", "docker:up": "node scripts/unified-deployment.js docker", "docker:down": "docker-compose down --remove-orphans", "docker:build": "docker-compose build --parallel", "docker:build:optimized": "bash scripts/build-optimized.sh", "docker:logs": "docker-compose logs -f", "docker:restart": "npm run docker:down && npm run docker:up", "docker:status": "docker-compose ps", "docker:optimize": "bash scripts/optimize-dockerfiles.sh", "docker:clean": "docker system prune -af --volumes", "deploy": "node scripts/unified-deployment.js", "deploy:local": "node scripts/unified-deployment.js local", "deploy:docker": "node scripts/unified-deployment.js docker", "deploy:k8s": "node scripts/unified-deployment.js k8s", "deploy:k8s:istio": "node scripts/unified-deployment.js k8s-istio", "deploy:k8s:consul": "node scripts/unified-deployment.js k8s-consul", "k8s:apply": "kubectl apply -f k8s/", "k8s:delete": "kubectl delete namespace antifraud", "k8s:status": "kubectl get pods -n antifraud", "k8s:logs": "kubectl logs -f deployment/api-gateway -n antifraud", "test": "node scripts/run-integration-tests.js all", "test:unit": "npm run test:services && npm run test:client", "test:contracts": "node scripts/run-integration-tests.js contracts", "test:integration": "node scripts/run-integration-tests.js integration", "test:e2e": "node scripts/run-integration-tests.js all", "test:services": "concurrently \"npm run test:api-gateway\" \"npm run test:auth\" \"npm run test:admin\" \"npm run test:community\" \"npm run test:link\" \"npm run test:news\" \"npm run test:chat\"", "test:api-gateway": "cd services/api-gateway && npm test", "test:auth": "cd services/auth-service && npm test", "test:admin": "cd services/admin-service && npm test", "test:chat": "cd services/chat-service && npm test", "test:community": "cd services/community-service && npm test", "test:link": "cd services/link-service && npm test", "test:news": "cd services/news-service && npm test", "test:client": "cd client && npm test", "lint": "npm run lint:services && npm run lint:client", "lint:services": "concurrently \"npm run lint:api-gateway\" \"npm run lint:auth\" \"npm run lint:admin\" \"npm run lint:community\" \"npm run lint:link\" \"npm run lint:news\" \"npm run lint:chat\"", "lint:api-gateway": "cd services/api-gateway && npm run lint", "lint:auth": "cd services/auth-service && npm run lint", "lint:admin": "cd services/admin-service && npm run lint", "lint:chat": "cd services/chat-service && npm run lint", "lint:community": "cd services/community-service && npm run lint", "lint:link": "cd services/link-service && npm run lint", "lint:news": "cd services/news-service && npm run lint", "lint:client": "cd client && npm run lint", "install:all": "npm install && npm run install:services && npm run install:client", "install:services": "concurrently \"npm run install:api-gateway\" \"npm run install:auth\" \"npm run install:admin\" \"npm run install:community\" \"npm run install:link\" \"npm run install:news\" \"npm run install:chat\"", "install:api-gateway": "cd services/api-gateway && npm install", "install:auth": "cd services/auth-service && npm install", "install:admin": "cd services/admin-service && npm install", "install:chat": "cd services/chat-service && npm install", "install:community": "cd services/community-service && npm install", "install:link": "cd services/link-service && npm install", "install:news": "cd services/news-service && npm install", "install:client": "cd client && npm install", "db:migrate": "node scripts/migrate-firestore.js", "db:seed": "node scripts/seed-database.js", "db:analyze": "node scripts/analyze-production-db.js", "db:test": "node scripts/test-production-connection.js", "monitoring:start": "node scripts/monitoring/install-monitoring.js", "monitoring:status": "node scripts/check-monitoring-status.js", "monitoring:grafana": "open http://localhost:3001", "monitoring:prometheus": "open http://localhost:9090", "security:status": "curl -s http://localhost:8080/security/status | jq || curl -s http://localhost:8080/security/status", "circuit-breaker:status": "curl -s http://localhost:8080/circuit-breaker/status | jq || curl -s http://localhost:8080/circuit-breaker/status", "auth:redundancy": "curl -s http://localhost:8080/auth/redundancy/status | jq || curl -s http://localhost:8080/auth/redundancy/status", "service-mesh:status": "kubectl get pods -n istio-system || consul members", "health": "curl -s http://localhost:8080/health || echo API not running", "health:frontend": "curl -s http://localhost:3000 || echo Frontend not running", "health:all": "npm run health && npm run health:frontend", "status": "npm run health:all && npm run security:status", "logs": "npm run logs:docker", "logs:docker": "docker-compose logs -f", "logs:k8s": "kubectl logs -f deployment/api-gateway -n antifraud", "fix:ports": "node scripts/utils/fix-port-conflicts.js", "validate:ports": "node scripts/utils/validate-ports.js", "kill:all": "node scripts/utils/kill-all-services.js", "setup:env": "echo Please configure .env file with your Firebase credentials", "setup:tech-stack": "node scripts/setup-tech-stack.js", "info": "echo Frontend: http://localhost:3000, API Gateway: http://localhost:8080, Monitoring: http://localhost:3001", "open": "start http://localhost:3000 || open http://localhost:3000", "open:api": "start http://localhost:8080 || open http://localhost:8080", "open:monitoring": "start http://localhost:3001 || open http://localhost:3001", "help": "node scripts/unified-deployment.js help"}, "dependencies": {"concurrently": "^8.2.2", "crypto": "^1.0.1", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "opossum": "^8.0.0", "prom-client": "^15.1.0", "redis": "^4.6.10", "uuid": "^11.1.0"}, "devDependencies": {"@pact-foundation/pact": "^12.1.0", "@pact-foundation/pact-node": "^10.17.7", "concurrently": "^8.2.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "workspaces": ["client", "services/*"], "keywords": ["microservices", "anti-fraud", "security", "circuit-breaker", "event-driven", "saga-pattern", "service-mesh", "contract-testing"], "author": "Anti-Fraud Platform Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/VinkRasengan/backup.git"}, "config": {"techStack": {"circuitBreaker": true, "eventBus": true, "saga": true, "serviceAuth": true, "authRedundancy": true, "contractTesting": true, "integrationTesting": true, "serviceMesh": "optional"}, "deployment": {"defaultMode": "local", "supportedModes": ["local", "docker", "k8s", "k8s-istio", "k8s-consul"]}}}