import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../context/AuthContext';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

export const useBatchVotes = () => {
    const [votesData, setVotesData] = useState({});
    const [userVotesData, setUserVotesData] = useState({});
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const { user } = useAuth();

    // Cache to avoid duplicate requests
    const [requestedPostIds, setRequestedPostIds] = useState(new Set());

    const fetchBatchVotes = useCallback(async (postIds) => {
        if (!postIds || postIds.length === 0) return;

        // Filter out already requested posts
        const newPostIds = postIds.filter(id => !requestedPostIds.has(id));
        if (newPostIds.length === 0) return;

        setLoading(true);
        setError(null);

        try {
            console.log('🚀 Fetching batch votes for', newPostIds.length, 'posts');

            // Split into chunks of 10 (Firestore 'in' query limit)
            const chunks = [];
            for (let i = 0; i < newPostIds.length; i += 10) {
                chunks.push(newPostIds.slice(i, i + 10));
            }

            const promises = chunks.map(async (chunk) => {
                const response = await fetch(`${API_BASE_URL}/community/votes/batch`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ postIds: chunk }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return response.json();
            });

            const results = await Promise.all(promises);
            
            // Merge all results
            const newVotesData = {};
            results.forEach(result => {
                if (result.success) {
                    Object.assign(newVotesData, result.data);
                }
            });

            setVotesData(prev => ({ ...prev, ...newVotesData }));
            setRequestedPostIds(prev => new Set([...prev, ...newPostIds]));

            // Fetch user votes if authenticated
            if (user) {
                await fetchBatchUserVotes(newPostIds);
            }

        } catch (err) {
            console.error('Error fetching batch votes:', err);
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, [requestedPostIds, user]);

    const fetchBatchUserVotes = useCallback(async (postIds) => {
        if (!user || !postIds || postIds.length === 0) return;

        try {
            console.log('🚀 Fetching batch user votes for', postIds.length, 'posts');

            // Split into chunks of 10
            const chunks = [];
            for (let i = 0; i < postIds.length; i += 10) {
                chunks.push(postIds.slice(i, i + 10));
            }

            const promises = chunks.map(async (chunk) => {
                const response = await fetch(`${API_BASE_URL}/community/votes/batch/user`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${user.token}`,
                    },
                    body: JSON.stringify({ postIds: chunk }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return response.json();
            });

            const results = await Promise.all(promises);
            
            // Merge all results
            const newUserVotesData = {};
            results.forEach(result => {
                if (result.success) {
                    Object.assign(newUserVotesData, result.data);
                }
            });

            setUserVotesData(prev => ({ ...prev, ...newUserVotesData }));

        } catch (err) {
            console.error('Error fetching batch user votes:', err);
        }
    }, [user]);

    // Get votes for a specific post
    const getVotesForPost = useCallback((postId) => {
        return votesData[postId] || { safe: 0, unsafe: 0, suspicious: 0, total: 0 };
    }, [votesData]);

    // Get user vote for a specific post
    const getUserVoteForPost = useCallback((postId) => {
        return userVotesData[postId] || null;
    }, [userVotesData]);

    // Clear cache (useful for refreshing data)
    const clearCache = useCallback(() => {
        setVotesData({});
        setUserVotesData({});
        setRequestedPostIds(new Set());
    }, []);

    // Preload votes for posts that are about to be visible
    const preloadVotes = useCallback((postIds) => {
        const newIds = postIds.filter(id => !requestedPostIds.has(id));
        if (newIds.length > 0) {
            fetchBatchVotes(newIds);
        }
    }, [fetchBatchVotes, requestedPostIds]);

    return {
        votesData,
        userVotesData,
        loading,
        error,
        fetchBatchVotes,
        getVotesForPost,
        getUserVoteForPost,
        clearCache,
        preloadVotes,
        setVotesData,
        setUserVotesData
    };
};

// Hook for individual post vote management
export const usePostVote = (postId) => {
    const {
        getVotesForPost,
        getUserVoteForPost,
        fetchBatchVotes,
        setVotesData,
        setUserVotesData
    } = useBatchVotes();
    const [localLoading, setLocalLoading] = useState(false);

    // Ensure votes are loaded for this post
    useEffect(() => {
        if (postId) {
            fetchBatchVotes([postId]);
        }
    }, [postId, fetchBatchVotes]);

    const votes = getVotesForPost(postId);
    const userVote = getUserVoteForPost(postId);

    const submitVote = useCallback(async (voteType) => {
        console.log('🗳️ useBatchVotes.submitVote called:', { postId, voteType });

        if (!postId) {
            console.log('❌ No postId provided');
            return;
        }

        // Get user info
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        if (!user.uid && !user.id) {
            console.log('❌ No user logged in');
            throw new Error('User not logged in');
        }

        // OPTIMISTIC UPDATE - Update UI immediately
        const currentVotes = getVotesForPost(postId);
        const currentUserVote = getUserVoteForPost(postId);

        console.log('🔄 Optimistic update:', {
            currentVotes,
            currentUserVote,
            newVoteType: voteType
        });

        // Calculate optimistic vote changes
        let optimisticVotes = { ...currentVotes };
        let optimisticUserVote = voteType;

        // Remove previous vote if exists
        if (currentUserVote) {
            if (currentUserVote === 'upvote' && optimisticVotes.safe > 0) {
                optimisticVotes.safe -= 1;
            } else if (currentUserVote === 'downvote' && optimisticVotes.unsafe > 0) {
                optimisticVotes.unsafe -= 1;
            }
        }

        // Add new vote (unless it's the same as current)
        if (currentUserVote === voteType) {
            // Removing vote (toggle off)
            optimisticUserVote = null;
        } else {
            // Adding new vote
            if (voteType === 'upvote') {
                optimisticVotes.safe = (optimisticVotes.safe || 0) + 1;
            } else if (voteType === 'downvote') {
                optimisticVotes.unsafe = (optimisticVotes.unsafe || 0) + 1;
            }
        }

        // Update UI immediately
        setVotesData(prev => ({
            ...prev,
            [postId]: optimisticVotes
        }));
        setUserVotesData(prev => ({
            ...prev,
            [postId]: optimisticUserVote
        }));

        setLocalLoading(true);
        try {
            // Get auth token
            const token = localStorage.getItem('authToken') ||
                         localStorage.getItem('backendToken') ||
                         localStorage.getItem('token');

            console.log('🔑 Auth info:', { hasToken: !!token, userId: user.uid || user.id, userEmail: user.email });

            const requestBody = {
                voteType,
                userId: user.uid || user.id,
                userEmail: user.email
            };

            console.log('📤 Batch vote request:', {
                url: `http://localhost:8080/api/votes/${postId}`,
                body: requestBody
            });

            // Try direct community service first for debugging
            console.log('🔧 Trying direct community service...');
            const directUrl = `http://localhost:3003/votes/${postId}`;
            console.log('📤 Direct request URL:', directUrl);

            // Create AbortController for timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

            // Try direct community service first for debugging
            const response = await fetch(`http://localhost:3003/votes/${postId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...(token && { 'Authorization': `Bearer ${token}` })
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            console.log('📥 Batch vote response:', { status: response.status, ok: response.ok });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('❌ Batch vote error response:', errorText);
                throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
            }

            const responseData = await response.json();
            console.log('✅ Batch vote success:', responseData);

            // Update with real data from API response if available
            if (responseData.success && responseData.data) {
                console.log('🔄 Updating with real API data...');

                // Update votes data with real response
                if (responseData.data.statistics) {
                    setVotesData(prev => ({
                        ...prev,
                        [postId]: {
                            safe: responseData.data.statistics.upvotes || 0,
                            unsafe: responseData.data.statistics.downvotes || 0,
                            suspicious: responseData.data.statistics.suspicious || 0,
                            total: responseData.data.statistics.total || 0
                        }
                    }));
                }

                // Update user vote with real response
                if (responseData.data.userVote !== undefined) {
                    setUserVotesData(prev => ({
                        ...prev,
                        [postId]: responseData.data.userVote
                    }));
                }
            }

            console.log('✅ Vote process completed successfully');
        } catch (error) {
            if (error.name === 'AbortError') {
                console.error('❌ Batch vote request timed out after 10 seconds');
                throw new Error('Request timed out. Please try again.');
            }
            console.error('❌ Error submitting batch vote:', error);

            // REVERT optimistic updates on error
            console.log('🔄 Reverting optimistic updates due to error...');
            setVotesData(prev => ({
                ...prev,
                [postId]: currentVotes
            }));
            setUserVotesData(prev => ({
                ...prev,
                [postId]: currentUserVote
            }));

            throw error;
        } finally {
            setLocalLoading(false);
        }
    }, [postId, fetchBatchVotes]);

    return {
        votes,
        userVote,
        loading: localLoading,
        submitVote,
    };
};

export default useBatchVotes;
