# =============================================================================
# Anti-Fraud Platform - Environment Configuration Template
# =============================================================================
# Copy this file to .env and fill in your actual values

# Environment
NODE_ENV=development

# Firebase Configuration (Required)
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour-Private-Key-Here\n-----END PRIVATE KEY-----\n"

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters-long
JWT_EXPIRE=7d

# API Keys (Optional - will use mock data if not provided)
GEMINI_API_KEY=your-gemini-api-key
VIRUSTOTAL_API_KEY=your-virustotal-api-key
SCAMADVISER_API_KEY=your-scamadviser-api-key
SCREENSHOTLAYER_API_KEY=your-screenshotlayer-api-key
NEWSAPI_API_KEY=your-newsapi-api-key
NEWSDATA_API_KEY=your-newsdata-api-key

# React App Configuration
REACT_APP_API_URL=http://localhost:8080
REACT_APP_FIREBASE_API_KEY=your-firebase-web-api-key
REACT_APP_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your-project-id

# Service URLs (automatically configured by deployment scripts)
AUTH_SERVICE_URL=http://localhost:3001
LINK_SERVICE_URL=http://localhost:3002
COMMUNITY_SERVICE_URL=http://localhost:3003
CHAT_SERVICE_URL=http://localhost:3004
NEWS_SERVICE_URL=http://localhost:3005
ADMIN_SERVICE_URL=http://localhost:3006

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# Database Configuration
USE_FIREBASE_EMULATOR=false

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# Logging
LOG_LEVEL=info

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Email Configuration (Optional - for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-gmail-app-password

# API Timeouts (milliseconds)
API_TIMEOUT=30000
SECURITY_ANALYSIS_TIMEOUT=45000

# Production/Development Flags
ENABLE_RATE_LIMITING=true
USE_MOCK_DATA_FALLBACK=true

# Additional Security APIs (Optional but recommended)
PHISHTANK_API_KEY=your-phishtank-api-key
GOOGLE_SAFEBROWSING_API_KEY=your-google-safebrowsing-api-key
IPQUALITYSCORE_API_KEY=your-ipqualityscore-api-key
CRIMINALIP_API_KEY=your-criminalip-api-key

# Additional External APIs (Optional)
HUDSONROCK_API_KEY=your-hudsonrock-api-key
