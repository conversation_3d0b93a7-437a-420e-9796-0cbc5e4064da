{"name": "factcheck-platform", "version": "2.0.0", "description": "FactCheck Anti-Fraud Platform - Enhanced Microservices Architecture with Advanced Tech Stack", "private": true, "scripts": {"start": "node scripts/simple-start.js", "stop": "node scripts/simple-stop.js", "restart": "npm stop && npm start", "status": "node scripts/simple-status.js", "setup": "npm install && npm run install:all", "install:all": "npm install && cd client && npm install && cd ../services/api-gateway && npm install && cd ../auth-service && npm install", "docker": "node scripts/antifraud.js docker", "docker:logs": "node scripts/antifraud.js logs", "deploy": "node scripts/antifraud.js docker", "deploy:k8s": "node scripts/antifraud.js k8s", "deploy:k8s:istio": "node scripts/antifraud.js k8s-istio", "deploy:k8s:consul": "node scripts/antifraud.js k8s-consul", "k8s:apply": "kubectl apply -f k8s/", "k8s:delete": "kubectl delete namespace antifraud", "k8s:status": "kubectl get pods -n antifraud", "k8s:logs": "kubectl logs -f deployment/api-gateway -n antifraud", "test": "node scripts/antifraud.js test", "test:unit": "node scripts/antifraud.js test-unit", "test:contract": "node scripts/antifraud.js test-contract", "test:integration": "node scripts/antifraud.js test-integration", "test:services": "concurrently \"npm run test:api-gateway\" \"npm run test:auth\" \"npm run test:admin\" \"npm run test:community\" \"npm run test:link\" \"npm run test:news\" \"npm run test:chat\"", "test:api-gateway": "cd services/api-gateway && npm test", "test:auth": "cd services/auth-service && npm test", "test:admin": "cd services/admin-service && npm test", "test:chat": "cd services/chat-service && npm test", "test:community": "cd services/community-service && npm test", "test:link": "cd services/link-service && npm test", "test:news": "cd services/news-service && npm test", "test:client": "cd client && npm test", "health": "node scripts/simple-status.js", "logs": "echo Use Docker mode for logs: npm run docker && docker-compose logs -f", "fix-ports": "node scripts/simple-stop.js", "info": "echo Frontend: http://localhost:3000, API Gateway: http://localhost:8080", "open": "start http://localhost:3000 || open http://localhost:3000", "help": "node scripts/antifraud.js help"}, "dependencies": {"concurrently": "^8.2.2", "dotenv": "^16.5.0", "prom-client": "^15.1.0", "redis": "^4.6.10", "opossum": "^8.0.0", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"@pact-foundation/pact": "^12.1.0", "@pact-foundation/pact-node": "^10.17.7", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "workspaces": ["client", "services/*"], "keywords": ["microservices", "anti-fraud", "security", "circuit-breaker", "event-driven", "saga-pattern", "service-mesh", "contract-testing"], "author": "Anti-Fraud Platform Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/VinkRasengan/backup.git"}, "config": {"techStack": {"circuitBreaker": true, "eventBus": true, "saga": true, "serviceAuth": true, "authRedundancy": true, "contractTesting": true, "integrationTesting": true, "serviceMesh": "optional"}, "deployment": {"defaultMode": "local", "supportedModes": ["local", "docker", "k8s", "k8s-istio", "k8s-consul"]}}}