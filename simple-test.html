<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voting & Commenting Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success { border-color: #28a745; background-color: #d4edda; }
        .error { border-color: #dc3545; background-color: #f8d7da; }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🧪 Voting & Commenting API Test</h1>
    
    <div class="test-section">
        <h2>🗳️ Voting Tests</h2>
        <div>
            <label>Link ID:</label>
            <input type="text" id="linkId" value="test-link-123" placeholder="Enter link ID">
        </div>
        <div>
            <label>User ID:</label>
            <input type="text" id="userId" value="test-user-123" placeholder="Enter user ID">
        </div>
        <div>
            <label>User Email:</label>
            <input type="text" id="userEmail" value="<EMAIL>" placeholder="Enter user email">
        </div>
        
        <button class="test-button" onclick="testUpvote()">Test Upvote</button>
        <button class="test-button" onclick="testDownvote()">Test Downvote</button>
        <button class="test-button" onclick="testGetVoteStats()">Get Vote Stats</button>
        <button class="test-button" onclick="testGetOptimizedVotes()">Get Optimized Votes</button>
        
        <div id="voteResults"></div>
    </div>

    <div class="test-section">
        <h2>💬 Commenting Tests</h2>
        <div>
            <label>Comment Content:</label>
            <textarea id="commentContent" placeholder="Enter comment content">This is a test comment from the HTML test page.</textarea>
        </div>
        
        <button class="test-button" onclick="testAddComment()">Add Comment</button>
        <button class="test-button" onclick="testGetComments()">Get Comments</button>
        
        <div id="commentResults"></div>
    </div>

    <div class="test-section">
        <h2>🔗 Direct Community Service Tests</h2>
        <button class="test-button" onclick="testDirectVoteStats()">Direct Vote Stats</button>
        <button class="test-button" onclick="testDirectComment()">Direct Comment</button>
        
        <div id="directResults"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';
        const COMMUNITY_BASE = 'http://localhost:3003';

        function getLinkId() { return document.getElementById('linkId').value; }
        function getUserId() { return document.getElementById('userId').value; }
        function getUserEmail() { return document.getElementById('userEmail').value; }
        function getCommentContent() { return document.getElementById('commentContent').value; }

        function displayResult(containerId, result, isError = false) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.textContent = typeof result === 'object' ? JSON.stringify(result, null, 2) : result;
            container.appendChild(div);
        }

        async function testUpvote() {
            try {
                const response = await fetch(`${API_BASE}/api/votes/${getLinkId()}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        voteType: 'upvote',
                        userId: getUserId(),
                        userEmail: getUserEmail()
                    })
                });
                
                const data = await response.json();
                displayResult('voteResults', `✅ Upvote Response (${response.status}): ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                displayResult('voteResults', `❌ Upvote Error: ${error.message}`, true);
            }
        }

        async function testDownvote() {
            try {
                const response = await fetch(`${API_BASE}/api/votes/${getLinkId()}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        voteType: 'downvote',
                        userId: getUserId(),
                        userEmail: getUserEmail()
                    })
                });
                
                const data = await response.json();
                displayResult('voteResults', `✅ Downvote Response (${response.status}): ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                displayResult('voteResults', `❌ Downvote Error: ${error.message}`, true);
            }
        }

        async function testGetVoteStats() {
            try {
                const response = await fetch(`${API_BASE}/api/votes/${getLinkId()}/stats`);
                const data = await response.json();
                displayResult('voteResults', `✅ Vote Stats Response (${response.status}): ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                displayResult('voteResults', `❌ Vote Stats Error: ${error.message}`, true);
            }
        }

        async function testGetOptimizedVotes() {
            try {
                const response = await fetch(`${API_BASE}/api/votes/${getLinkId()}/optimized?userId=${getUserId()}`);
                const data = await response.json();
                displayResult('voteResults', `✅ Optimized Votes Response (${response.status}): ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                displayResult('voteResults', `❌ Optimized Votes Error: ${error.message}`, true);
            }
        }

        async function testAddComment() {
            try {
                const response = await fetch(`${API_BASE}/api/comments`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        postId: getLinkId(),
                        content: getCommentContent(),
                        userId: getUserId(),
                        userEmail: getUserEmail(),
                        displayName: 'Test User'
                    })
                });
                
                const data = await response.json();
                displayResult('commentResults', `✅ Add Comment Response (${response.status}): ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                displayResult('commentResults', `❌ Add Comment Error: ${error.message}`, true);
            }
        }

        async function testGetComments() {
            try {
                const response = await fetch(`${API_BASE}/api/comments/${getLinkId()}?page=1&limit=10`);
                const data = await response.json();
                displayResult('commentResults', `✅ Get Comments Response (${response.status}): ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                displayResult('commentResults', `❌ Get Comments Error: ${error.message}`, true);
            }
        }

        async function testDirectVoteStats() {
            try {
                const response = await fetch(`${COMMUNITY_BASE}/votes/${getLinkId()}/stats`);
                const data = await response.json();
                displayResult('directResults', `✅ Direct Vote Stats Response (${response.status}): ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                displayResult('directResults', `❌ Direct Vote Stats Error: ${error.message}`, true);
            }
        }

        async function testDirectComment() {
            try {
                const response = await fetch(`${COMMUNITY_BASE}/comments`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        postId: getLinkId(),
                        content: getCommentContent(),
                        userId: getUserId(),
                        userEmail: getUserEmail(),
                        displayName: 'Direct Test User'
                    })
                });
                
                const data = await response.json();
                displayResult('directResults', `✅ Direct Comment Response (${response.status}): ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                displayResult('directResults', `❌ Direct Comment Error: ${error.message}`, true);
            }
        }

        // Clear results function
        function clearResults() {
            document.getElementById('voteResults').innerHTML = '';
            document.getElementById('commentResults').innerHTML = '';
            document.getElementById('directResults').innerHTML = '';
        }
    </script>
    
    <div style="text-align: center; margin-top: 20px;">
        <button class="test-button" onclick="clearResults()">Clear Results</button>
    </div>
</body>
</html>
