# Anti-Fraud Platform

## 🚀 One Command Does Everything

```bash
# First time setup
npm run setup

# Start development
npm start

# Deploy with Docker  
npm run docker

# Deploy to Kubernetes
npm run deploy:k8s

# Run all tests
npm test

# Check status
npm run status

# Get help
npm run help
```

## 📋 All Available Commands

| Command | Description |
|---------|-------------|
| `npm run setup` | Complete setup and installation |
| `npm start` | Start all services locally |
| `npm run dev` | Start in development mode |
| `npm stop` | Stop all services |
| `npm restart` | Restart all services |
| `npm run docker` | Deploy with Docker Compose |
| `npm run deploy:k8s` | Deploy to Kubernetes |
| `npm test` | Run all tests |
| `npm run status` | Show system status |
| `npm run health` | Health check |
| `npm run logs` | Show logs |
| `npm run fix-ports` | Fix port conflicts |
| `npm run clean` | Clean up everything |
| `npm run help` | Show detailed help |

## 🏗️ Architecture

- **Frontend**: React (port 3000)
- **API Gateway**: Express.js (port 8080) 
- **Auth Service**: Node.js (port 3001)
- **Link Service**: Node.js (port 3002)
- **Community Service**: Node.js (port 3003)
- **Chat Service**: Node.js (port 3004)
- **News Service**: Node.js (port 3005)
- **Admin Service**: Node.js (port 3006)

## 🔧 Tech Stack Features

✅ **Circuit Breaker Pattern** - Prevents cascade failures  
✅ **Event-Driven Architecture** - Redis pub/sub  
✅ **Service Authentication** - Individual service keys  
✅ **Auth Service Redundancy** - Multiple auth instances  
✅ **Contract Testing** - Pact framework  
✅ **Integration Testing** - E2E pipeline  
✅ **Monitoring** - Prometheus + Grafana  
✅ **Service Mesh Ready** - Istio/Consul support  

## 🚀 Quick Start

```bash
# Clone and setup
git clone <repo-url>
cd backup
npm run setup

# Start development
npm start

# Open browser
open http://localhost:3000
```

## 📊 Monitoring

- **Application**: http://localhost:3000
- **API Gateway**: http://localhost:8080  
- **Monitoring**: http://localhost:9090
- **Status**: `npm run status`

## 🧪 Testing

```bash
npm test              # All tests
npm run test:unit     # Unit tests only
npm run test:contract # Contract tests only
npm run test:integration # Integration tests only
```

## 🐳 Docker Deployment

```bash
npm run docker        # Start with Docker
npm run logs          # View logs
npm stop              # Stop everything
```

## ☸️ Kubernetes Deployment

```bash
npm run deploy:k8s           # Basic K8s
npm run deploy:k8s:istio     # With Istio service mesh
npm run deploy:k8s:consul    # With Consul Connect
```

## 🛠️ Troubleshooting

```bash
npm run fix-ports     # Fix port conflicts
npm run clean         # Clean everything
npm run status        # Check what's running
npm run health        # Health check
```

## 📄 License

MIT License
